import axios from 'axios';
const token = localStorage.getItem('token');
// import { reqUtil } from 'mtex-rams-core';
// const { decodeResult, tryEncodeParam } = reqUtil;

// const encryption = !true;

// post 请求
const aiConfigPost = function ({ url, params = {}, headers = {}, gateWay = '', cancelToken = null }) {
    let newParams = params;
    if (Object.keys(headers).length > 0) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        const config = {
            headers: {
                token: token
            },
            timeout: 300000 // 5分钟超时
        };

        // 如果提供了cancelToken，添加到配置中
        if (cancelToken) {
            config.cancelToken = cancelToken;
        }

        axios
            .post(gateWay + url, newParams, config)
            .then((res) => {
                let newRes = res.data;
                resolve(newRes);
                return;
            })
            .catch((err) => {
                // 检查是否是取消请求
                if (axios.isCancel(err)) {
                    reject({
                        success: 999,
                        errorMessage: '请求已取消',
                        isCancelled: true
                    });
                } else {
                    reject({
                        success: 111,
                        errorMessage: err.message
                    });
                }
            });
    });
};
// get 请求
export const aiConfigGet = function ({ url, params = {}, headers = {}, gateWay = '' }) {
    let newParams = params;
    if (Object.keys(headers).length > 0) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        axios
            .get(gateWay + url, {
                params: newParams,
                headers: {
                    token: token
                },
                timeout: 300000 // 5分钟超时
            })
            .then((res) => {
                let newRes = res.data;
                resolve(newRes);
            })
            .catch((err) => {
                reject('');
            });
    });
};
// 流式 post 请求
export const aiConfigStreamPost = function ({ url, params = {}, headers = {}, gateWay = '', cancelToken = null, onData = null, onComplete = null, onError = null }) {
    let newParams = params;
    if (Object.keys(headers).length > 0) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }

    return new Promise((resolve, reject) => {
        const config = {
            headers: {
                token: token,
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
            },
            timeout: 300000, // 5分钟超时
            responseType: 'stream'
        };

        // 如果提供了cancelToken，添加到配置中
        if (cancelToken) {
            config.cancelToken = cancelToken;
        }

        // 使用 fetch API 来处理流式响应
        const fetchUrl = gateWay + url;
        const fetchOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'token': token,
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
            },
            body: JSON.stringify(newParams)
        };

        // 创建 AbortController 用于取消请求
        const abortController = new AbortController();
        fetchOptions.signal = abortController.signal;

        // 如果有 cancelToken，监听取消事件
        if (cancelToken) {
            cancelToken.promise.then(() => {
                abortController.abort();
            });
        }



        // 处理单行数据的辅助函数
        const processDataLine = (dataStr) => {
            // 检查是否为结束标记
            if (dataStr === '[DONE]') {
                if (onComplete) onComplete();
                return true; // 表示应该结束
            }

            // 尝试解析 JSON 数据
            try {
                const data = JSON.parse(dataStr);

                // 检查是否为结束事件
                if (data.eventType === 'MESSAGE_END' || data.event === 'message_end') {
                    if (onComplete) onComplete();
                    return true; // 表示应该结束
                }

                // 提取 answer 字段（如果存在）
                if (data.answer && onData) {
                    onData({ text: data.answer });
                } else if (onData) {
                    // 兼容其他格式的数据
                    onData(data);
                }
            } catch (e) {
                // JSON 解析失败，直接忽略
            }

            return false; // 表示继续处理
        };

        fetch(fetchUrl, fetchOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                const processStream = () => {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            // 流式响应结束
                            if (onComplete) onComplete();
                            resolve({ success: true, message: '流式响应完成' });
                            return;
                        }

                        // 解码数据块
                        const chunk = decoder.decode(value, { stream: true });
                        buffer += chunk;

                        // 处理 Server-Sent Events 格式
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

                        for (const line of lines) {
                            // 只处理 data: 行，忽略其他所有行（包括 event: 行）
                            if (line.startsWith('data:')) {
                                const dataStr = line.substring(5).trim();
                                const shouldEnd = processDataLine(dataStr);
                                if (shouldEnd) {
                                    resolve({ success: true, message: '流式响应完成' });
                                    return;
                                }
                            }
                        }

                        // 继续读取下一个数据块
                        return processStream();
                    });
                };

                return processStream();
            })
            .catch((err) => {
                // 检查是否是取消请求
                if (err.name === 'AbortError' || (cancelToken && cancelToken.reason)) {
                    const cancelError = {
                        success: 999,
                        errorMessage: '请求已取消',
                        isCancelled: true
                    };
                    if (onError) onError(cancelError);
                    reject(cancelError);
                } else {
                    const error = {
                        success: 111,
                        errorMessage: err.message
                    };
                    if (onError) onError(error);
                    reject(error);
                }
            });
    });
};

export default aiConfigPost;
