<template>
    <div class="main-content">
        <div class="content-wrapper">
            <div class="welcome-index">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <div class="welcome-icon">
                        <img src="~@/img/main/logo-big.png" alt="logo" class="logo-main" />
                    </div>
                    <h1 class="welcome-title">{{ welcomeConfig.title }}</h1>
                    <p class="welcome-desc">{{ welcomeConfig.description }}</p>
                </div>

                <!-- 功能卡片区域 -->
                <div class="feature-cards">
                    <el-card
                        class="feature-card"
                        :style="{ '--card-bg': `url(${card.bg})` }"
                        v-for="(card, index) in featureCards"
                        :key="index"
                        shadow="never"
                        :body-style="{ padding: 0, height: '100%' }"
                        @click.native="handleCardClick(card)"
                    >
                        <div class="card-content">
                            <div class="card-icon">
                                <img :src="card.icon" :alt="card.title" />
                            </div>
                            <h3 class="card-title">{{ card.title }}</h3>
                            <p class="card-desc text-ellipsis">{{ card.description }}</p>
                        </div>
                    </el-card>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-section">
                <MessageInput
                    ref="messageInput"
                    :placeholder="inputConfig.placeholder"
                    :model-options="modelOptions"
                    :defaultModel="defaultModel"
                    :selectedModel="selectedModel"
                    :show-mic-button="true"
                    :show-model-details="true"
                    popper-class="dataUsageAssistant-theme model-select-popper"
                    @send-message="handleSendMessage"
                    @mic-click="handleMicClick"
                    @model-change="handleModelChange"
                />
            </div>
        </div>
    </div>
</template>

<script>
import MessageInput from './MessageInput.vue';

export default {
    name: 'MainContent',
    components: {
        MessageInput
    },
    props: {
        welcomeConfig: {
            type: Object,
            default: () => ({
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能对话新体验，助您轻松了解不同需求的适用数据，用AI开启高效便捷的数据订阅之旅'
            })
        },
        inputConfig: {
            type: Object,
            default: () => ({
                placeholder:
                    '请输入您的问题，Shift+Enter可换行，@后带能力名称可指定能力，输入后按Enter发送'
            })
        },
        featureCards: {
            type: Array,
            default: () => []
        },
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },
        selectedModel: {
            type: String,
            default: ''
        }
    },
    methods: {
        // 处理功能卡片点击
        handleCardClick(card) {
            this.$emit('card-click', card);

            // 根据卡片类型设置预设问题并切换到聊天页面
            const presetQuestions = {
                'location-capability':
                    '@位置能力使用推荐 提示词：位置能力使用推荐能力介绍词+简要操作+推荐问题等',
                'asset-subscription':
                    '@位置资产订购向导 提示词：位置资产订购向导能力介绍词+简要操作+推荐问题等',
                'business-requirements':
                    '@位置业务需求规格 提示词：位置业务需求规格能力介绍词+简要操作+推荐问题等'
            };

            if (presetQuestions[card.id]) {
                this.$refs.messageInput.setInputText(presetQuestions[card.id]);
            }
        },

        // 处理发送消息
        handleSendMessage(message) {
            this.$emit('send-message', message);
        },

        // 处理语音输入点击
        handleMicClick() {
            // 可以在这里添加语音输入逻辑
            console.log('语音输入点击');
        },

        // 处理模型变更
        handleModelChange(newModel) {
            this.$emit('model-change', newModel);
        }
    }
};
</script>

<style scoped lang="less">
// 主内容区域样式
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-wrapper {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        width: 100%;

        .welcome-index {
            min-height: 0;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100%;
            gap: 3.75rem;

            .welcome-section {
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;

                .welcome-icon {
                    width: 6.25rem;
                    height: 6.25rem;
                    margin-bottom: 1.875rem;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .welcome-title {
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 500;
                    font-size: 2rem;
                    color: #001024;
                    line-height: 2.5rem;
                    margin: 0 0 1.5rem 0;
                }

                .welcome-desc {
                    font-family: HONORSansCN, HONORSansCN;
                    font-weight: 400;
                    font-size: 1rem;
                    color: #666666;
                    line-height: 1.5rem;
                    margin-bottom: 0;
                }
            }

            .feature-cards {
                display: flex;
                gap: 1.5rem;

                .feature-card {
                    --card-bg: url('~@/img/main/card-bg-1.png');
                    width: 19rem;
                    height: 15.4375rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background-color: transparent;
                    background-image: var(--card-bg);
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    border: none;
                    &:hover {
                        transform: translateY(-0.125rem);
                    }

                    .card-content {
                        width: 100%;
                        height: 100%;
                        padding: 1.5rem;

                        .card-icon {
                            margin-bottom: 1.125rem;

                            img {
                                width: 3rem;
                                height: 3rem;
                            }
                        }

                        .card-title {
                            font-weight: 500;
                            font-size: 1.25rem;
                            color: #222222;
                            line-height: 1.75rem;
                            margin: 0 0 0.75rem 0;
                        }

                        .card-desc {
                            --line-clamp: 4;
                            font-weight: 400;
                            font-size: 0.875rem;
                            color: #666666;
                            line-height: 1.25rem;
                            margin: 0;
                        }
                    }
                }
            }
        }

        .input-section {
            margin-top: auto;
            padding-bottom: 1.5rem;
        }
    }
}
</style>
